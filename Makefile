# Simple Makefile for Windows C program
CC=gcc
CFLAGS=-Wall -Wextra
LIBS=-lgdi32 -luser32 -lkernel32
TARGET=window.exe
SOURCE=window.c

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE) $(LIBS)

# Clean build artifacts
clean:
	del $(TARGET) 2>nul || echo "No files to clean"

# Run the program
run: $(TARGET)
	./$(TARGET)

.PHONY: all clean run
